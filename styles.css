/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
    height: -webkit-fill-available;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover;
    color: #fff;
    min-height: 100vh;
    min-height: -webkit-fill-available;
    /* iOS Safari fix */
    padding-bottom: 100px;
    overflow-x: hidden;
    position: relative;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Mobile Safari specific fixes */
@supports (-webkit-touch-callout: none) {
    body {
        background-attachment: scroll;
        min-height: -webkit-fill-available;
    }

    .track-card,
    .search-box,
    .music-player {
        -webkit-backdrop-filter: blur(15px);
        backdrop-filter: blur(15px);
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    padding: 1.5rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 2rem;
    color: #7877c6;
}

.logo h1 {
    font-size: 2.2rem;
    font-weight: 800;
    background: linear-gradient(45deg, #7877c6, #78dbff, #ff77c6, #c677ff);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(120, 119, 198, 0.5);
}

@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.login-btn {
    background: linear-gradient(45deg, #7877c6, #78dbff);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 30px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.4);
    position: relative;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #78dbff, #7877c6);
    transition: left 0.5s ease;
    z-index: -1;
}

.login-btn:hover::before {
    left: 0;
}

.login-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(120, 219, 255, 0.6);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

/* Search Section */
.search-section {
    text-align: center;
    margin-bottom: 3rem;
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 18px 25px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-box:focus-within {
    border-color: #7877c6;
    box-shadow: 0 0 30px rgba(120, 119, 198, 0.5), 0 8px 32px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
}

.search-box i {
    color: #ccc;
    margin-right: 15px;
    font-size: 1.2rem;
}

.search-box input {
    flex: 1;
    background: none;
    border: none;
    color: white;
    font-size: 1.1rem;
    outline: none;
}

.search-box input::placeholder {
    color: #ccc;
}

.clear-search-btn {
    background: none;
    border: none;
    color: #ccc;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.clear-search-btn:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

/* Featured Section */
.featured-section h2,
.results-section h2 {
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    text-align: center;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 15px;
}

.section-header h2 {
    margin: 0;
    text-align: left;
}

.playlist-btn {
    background: linear-gradient(45deg, #1db954, #1ed760);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
}

.playlist-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(29, 185, 84, 0.4);
    background: linear-gradient(45deg, #1ed760, #1db954);
}

.track-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 3rem;
    padding: 0 10px;
}

.track-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Track Card */
.track-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.track-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(120, 119, 198, 0.15), rgba(120, 219, 255, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.track-card:hover::before {
    opacity: 1;
}

.track-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    border-color: #7877c6;
}

.track-card.active {
    border-color: #7877c6;
    background: rgba(120, 119, 198, 0.2);
    transform: translateY(-5px);
}

.track-card img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 15px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.track-card:hover img {
    transform: scale(1.05);
}

.track-card h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.track-card p {
    color: #ccc;
    font-size: 0.9rem;
}

/* Track Item (for search results) */
.track-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.track-item:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #1db954;
}

.track-item.active {
    border-color: #1db954;
    background: rgba(29, 185, 84, 0.2);
}

.track-item img {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-right: 15px;
    object-fit: cover;
}

.track-item .track-info {
    flex: 1;
}

.track-item h4 {
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.track-item p {
    color: #ccc;
    font-size: 0.9rem;
}

.track-item .duration {
    color: #ccc;
    font-size: 0.9rem;
    margin-left: 15px;
}

/* Music Player */
.music-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    z-index: 1000;
    box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.3);
}

.player-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    gap: 20px;
}

.track-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.track-info img {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.track-details h4 {
    margin-bottom: 5px;
    font-size: 1rem;
}

.track-details p {
    color: #ccc;
    font-size: 0.9rem;
}

.player-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.player-controls button {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.player-controls button:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.play-btn {
    background: linear-gradient(45deg, #7877c6, #78dbff) !important;
    font-size: 1.5rem !important;
    width: 50px;
    height: 50px;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.4);
}

.play-btn:hover {
    background: linear-gradient(45deg, #78dbff, #7877c6) !important;
    box-shadow: 0 6px 20px rgba(120, 219, 255, 0.6);
}

.player-progress {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-self: end;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    cursor: pointer;
    position: relative;
}

.progress {
    height: 100%;
    background: linear-gradient(45deg, #7877c6, #78dbff);
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s ease;
    box-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.volume-control input {
    width: 100px;
}

/* Loading */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(29, 185, 84, 0.3);
    border-top: 3px solid #1db954;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Spotify Embed Container */
.spotify-embed-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 95%;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 2px solid #7877c6;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    z-index: 2000;
    overflow: hidden;
}

.embed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(120, 119, 198, 0.1);
}

.embed-header h3 {
    color: #7877c6;
    font-size: 1.2rem;
    margin: 0;
}

.close-embed-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-embed-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.embed-content {
    padding: 20px;
    height: 450px;
}

.embed-content iframe {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        background-attachment: scroll;
        /* Fix for mobile */
        min-height: 100vh;
        min-height: -webkit-fill-available;
    }

    .container {
        padding: 0 15px;
    }

    .header .container {
        flex-direction: column;
        gap: 15px;
    }

    .logo h1 {
        font-size: 1.8rem;
    }

    .search-container {
        max-width: 100%;
    }

    .search-box {
        padding: 15px 20px;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }

    .section-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .section-header h2 {
        text-align: center;
        font-size: 1.5rem;
    }

    .track-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        padding: 0 5px;
    }

    .track-card {
        padding: 20px;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }

    .track-card img {
        height: 180px;
    }

    .spotify-embed-container {
        width: 98%;
        max-width: 500px;
    }

    .embed-content {
        height: 400px;
        padding: 15px;
    }

    .music-player {
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }

    .player-content {
        grid-template-columns: 1fr;
        gap: 15px;
        text-align: center;
    }

    .player-progress {
        justify-self: center;
    }

    .progress-bar {
        width: 200px;
    }

    .volume-control {
        justify-self: center;
    }
}

@media (max-width: 480px) {
    body {
        background-attachment: scroll;
        background-size: 100% 100%;
        padding-bottom: 120px;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .login-btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .search-box {
        padding: 12px 18px;
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .track-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .track-card {
        padding: 15px;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .track-card img {
        height: 160px;
    }

    .section-header h2 {
        font-size: 1.3rem;
    }

    .music-player {
        padding: 10px 15px;
        background: rgba(0, 0, 0, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .player-content {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .progress-bar {
        width: 150px;
    }

    .volume-control {
        display: none;
    }
}

/* Loading Animation Enhancement */
.loading {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.loading p {
    font-size: 1.1rem;
    color: #7877c6;
    font-weight: 600;
}

/* Scroll Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.track-card {
    animation: fadeInUp 0.6s ease-out;
}

.track-card:nth-child(1) {
    animation-delay: 0.1s;
}

.track-card:nth-child(2) {
    animation-delay: 0.2s;
}

.track-card:nth-child(3) {
    animation-delay: 0.3s;
}

.track-card:nth-child(4) {
    animation-delay: 0.4s;
}

.track-card:nth-child(5) {
    animation-delay: 0.5s;
}

.track-card:nth-child(6) {
    animation-delay: 0.6s;
}